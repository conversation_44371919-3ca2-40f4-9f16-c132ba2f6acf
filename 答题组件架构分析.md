# 答题组件架构分析

## 概述

本文档基于 `apps/stu/app/exercise/page.tsx` 和 `apps/stu/app/views/course/course-widgets-loader-view.tsx` 两个核心文件，梳理了学生端答题组件的完整架构，包括分层设计、依赖关系、数据流和组件类图。

## 1. 整体架构设计

### 1.1 MVVM 架构模式

答题系统采用 MVVM（Model-View-ViewModel）架构模式，实现了清晰的分层和职责分离：

```mermaid
graph TD
    A[View 视图层] --> B[ViewModel 视图模型层]
    B --> C[Model 数据模型层]
    C --> D[Backend API]
    
    A1[ExerciseView] --> B1[useQuestionSubmissionVM]
    A2[ChoiceQuestionView] --> B2[useChoiceQuestionViewModel]
    A3[CourseWidgetsLoaderView] --> B3[useCourseViewContext]
    
    B1 --> C1[useGetNextQuestion]
    B2 --> C2[useSubmitStudyAnswer]
    B3 --> C3[useCourseWidgetModel]
```

### 1.2 Context-First 架构

采用 Context-First 零参数架构，通过 Context 传递所有必要数据，避免 props drilling：

- **ExerciseContext**: 答题核心上下文
- **CourseViewContext**: 课程视图上下文  
- **QuestionViewContext**: 题目视图上下文
- **ClientProvider**: 客户端环境上下文

## 2. 分层架构详解

### 2.1 视图层 (View Layer)

#### 2.1.1 页面入口组件

```typescript
// 答题页面入口
ExercisePreviewPage
├── ExercisePageContent
    ├── BaseExerciseEntry (巩固/拓展练习)
    └── WrongBookExerciseEntry (错题本练习)

// 课程中的答题组件
CourseWidgetsLoaderView
├── WidgetLoader
    └── ExerciseInCourseView
```

#### 2.1.2 核心视图组件

```typescript
// 主答题视图
ExerciseView (packages/core/new-exercise/view/main-view.tsx)
├── ExerciseViewContent
    ├── BackButton
    ├── ProgressBar  
    ├── TimerDisplay
    ├── QuestionViewContent
    ├── TransitionView
    └── FeedbackView

// 题目视图
QuestionView
├── ChoiceQuestionView (选择题)
├── FillBlankQuestionView (填空题)
└── QuestionActionButtons
```

### 2.2 视图模型层 (ViewModel Layer)

#### 2.2.1 核心 ViewModels

```typescript
// 答案提交逻辑
useQuestionSubmissionVM
├── 策略模式验证
├── 统一提交流程
├── 进度条管理
└── 埋点追踪

// 选择题专用逻辑
useChoiceQuestionViewModel
├── 选项选择处理
├── 多选题逻辑
└── 母子题导航

// 课程序列管理
useCourseSequenceViewmodel
├── 组件切换逻辑
├── 进度管理
└── 状态同步
```

#### 2.2.2 专用 ViewModels

```typescript
useTimerVM          // 计时器管理
useExitViewModel    // 退出逻辑
useQuestionTrackVM  // 埋点追踪
useFillBlankViewModel // 填空题逻辑
```

### 2.3 状态管理层 (Store Layer)

#### 2.3.1 核心 Store

```typescript
// 题目状态管理 (基于 Preact Signals)
QuestionStore
├── questionStatus: Signal<QuestionStatus>
├── userAnswerDataMap: Signal<Map<string, Answer[]>>
├── currentQuestion: Signal<StudyQuestionInfo>
├── progressBarState: Signal<ProgressBarState>
├── timerState: Signal<TimerState>
└── transitionState: Signal<TransitionState>

// 预览模式状态
PreviewStore
├── previewConfig: Signal<PreviewConfig>
├── previewCurrentIndex: Signal<number>
└── previewQuestionList: Signal<Question[]>
```

### 2.4 数据模型层 (Model Layer)

#### 2.4.1 API 模型

```typescript
// 核心答题 API
useGetNextQuestion    // 获取下一题
useSubmitStudyAnswer  // 提交答案
useExitStudySession   // 退出学习

// 错题本 API  
useAddWrongQuestion     // 添加错题
useRemoveWrongQuestion  // 移除错题
useGetErrorReasonTags   // 获取错误原因标签

// 课程组件 API
useCourseWidgetModel  // 获取课程组件数据
useQuestionList       // 获取题目列表
```

## 3. 数据流架构

### 3.1 答题数据流

```mermaid
sequenceDiagram
    participant U as User
    participant V as View
    participant VM as ViewModel  
    participant S as Store
    participant M as Model
    participant API as Backend

    U->>V: 用户操作
    V->>VM: 触发事件
    VM->>S: 更新状态
    S->>V: 响应式更新
    VM->>M: 调用API
    M->>API: HTTP请求
    API->>M: 返回数据
    M->>S: 更新状态
    S->>V: 自动重渲染
```

### 3.2 课程组件数据流

```mermaid
sequenceDiagram
    participant CW as CourseWidgetsLoader
    participant WL as WidgetLoader
    participant CM as CourseWidgetModel
    participant EV as ExerciseView
    participant QS as QuestionStore

    CW->>WL: 渲染组件列表
    WL->>CM: 请求组件数据
    CM->>WL: 返回组件配置
    WL->>EV: 渲染答题组件
    EV->>QS: 初始化状态
    QS->>EV: 提供响应式状态
```

## 4. 组件类图

### 4.1 核心组件类图

```mermaid
classDiagram
    class ExerciseView {
        +studyType: StudyType
        +studySessionId: number
        +widgetIndex?: number
        +onComplete(): void
        +onBack(): void
        +render(): JSX.Element
    }
    
    class QuestionStore {
        +questionStatus: Signal~QuestionStatus~
        +currentQuestion: Signal~StudyQuestionInfo~
        +userAnswerDataMap: Signal~Map~
        +updateUserAnswer(): void
        +submitAnswer(): Promise~void~
        +getNextQuestion(): Promise~void~
    }
    
    class ExerciseContext {
        +questionStore: QuestionStore
        +studyType: StudyType
        +isPreview: boolean
        +clientContext: ClientContext
    }
    
    class CourseViewContext {
        +currentIndex: Signal~number~
        +widgetList: CourseWidget[]
        +next(): void
        +goto(index: number): void
        +exit(): void
    }
    
    ExerciseView --> QuestionStore
    ExerciseView --> ExerciseContext
    CourseWidgetsLoaderView --> CourseViewContext
    QuestionStore --> ExerciseModels
```

### 4.2 ViewModel 类图

```mermaid
classDiagram
    class QuestionSubmissionVM {
        +submitAnswer(): Promise~void~
        +submitSelfEvaluation(): Promise~void~
        +giveUpAnswer(): Promise~void~
        +validateAnswer(): boolean
    }
    
    class ChoiceQuestionViewModel {
        +handleOptionSelect(): void
        +isOptionSelected(): boolean
        +getSelectedOptions(): string[]
    }
    
    class TimerVM {
        +isTimerActive: boolean
        +handleTimeUpdate(): void
        +startTimer(): void
        +stopTimer(): void
    }
    
    QuestionSubmissionVM --> QuestionStore
    ChoiceQuestionViewModel --> QuestionStore
    TimerVM --> QuestionStore
```

## 5. 依赖关系图

### 5.1 模块依赖

```mermaid
graph TD
    A[apps/stu/exercise] --> B[packages/core/new-exercise]
    A --> C[apps/stu/models]
    A --> D[apps/stu/providers]
    
    B --> E[packages/core/types]
    B --> F[packages/core/enums]
    B --> G[packages/ui]
    
    C --> H[packages/core/new-exercise/models]
    D --> I[packages/core/context]
```

### 5.2 组件依赖层次

```mermaid
graph TD
    A[ExercisePreviewPage] --> B[BaseExerciseEntry]
    A --> C[WrongBookExerciseEntry]
    
    B --> D[ExerciseView]
    C --> D
    
    D --> E[QuestionViewContent]
    D --> F[ProgressBar]
    D --> G[TimerDisplay]
    
    E --> H[ChoiceQuestionView]
    E --> I[FillBlankQuestionView]
    
    J[CourseWidgetsLoaderView] --> K[WidgetLoader]
    K --> L[ExerciseInCourseView]
    L --> D
```

## 6. 设计模式应用

### 6.1 策略模式
- **用途**: 不同题型的答案验证逻辑
- **实现**: `ValidationStrategy` 接口，支持选择题、填空题等不同验证策略

### 6.2 观察者模式  
- **用途**: 响应式状态管理
- **实现**: 基于 Preact Signals 的细粒度状态订阅

### 6.3 工厂模式
- **用途**: 根据学习类型创建不同的答题组件
- **实现**: `ExercisePageContent` 中的条件渲染逻辑

### 6.4 上下文模式
- **用途**: 跨组件状态共享
- **实现**: Context-First 架构，避免 props drilling

## 7. 关键特性

### 7.1 响应式状态管理
- 基于 Preact Signals 实现细粒度响应式更新
- 避免不必要的组件重渲染
- 支持复杂状态的高效管理

### 7.2 多模式支持
- **AI课程模式**: 课程中的练习组件
- **巩固练习**: 独立的练习模式  
- **拓展练习**: 扩展学习内容
- **错题本**: 错题复习模式

### 7.3 预览与正式模式
- **预览模式**: 查看已完成的题目和答案
- **正式模式**: 实时答题和提交

### 7.4 性能优化
- 组件懒加载和条件渲染
- SWR 缓存和重复请求去重
- 虚拟滚动和内存管理

## 8. 核心类型定义

### 8.1 答题相关类型

```typescript
// 题目状态枚举
enum QuestionStatus {
  LOADING = "loading",
  ANSWERING = "answering",
  SUBMITTED = "submitted",
  UNCERTAIN = "uncertain"
}

// 学习类型
enum StudyType {
  AI_COURSE = 1,
  REINFORCEMENT_EXERCISE = 2,
  EXPAND_EXERCISE = 3,
  WRONG_QUESTION_BANK = 8
}

// 答案验证类型
enum AnswerVerifyType {
  NOT_ANSWERED = 0,
  CORRECT = 1,
  WRONG = 2,
  PARTIAL_CORRECT = 3
}

// 输入模式类型
enum InputModeType {
  KEYBOARD = 1,
  WHITEBOARD = 2,
  CAMERA = 3
}
```

### 8.2 核心接口定义

```typescript
// 答题初始化参数
interface ExerciseInitParams {
  studySessionId: number;
  studyType: StudyType;
  widgetIndex?: number;
  initialQuestionData?: ApiGetNextQuestionData;
  onComplete: (totalTimeSpent?: number) => void;
  onBack: () => void;
  clientContext: ClientContext;
  displayConfig?: ExerciseDisplayConfig;
  isPreview?: boolean;
  previewConfig?: PreviewConfig;
}

// 学生答案格式
interface Answer {
  index: number;
  type: InputModeType;
  content: string;
  answerResult?: AnswerVerifyType;
  selfEvaluation?: SelfEvaluateType;
  whiteBoardData?: WhiteBoardData;
  cameraData?: ImageFile[];
}

// 题目信息
interface StudyQuestionInfo {
  questionId: string;
  questionType: QuestionType;
  content: string;
  options?: QuestionOption[];
  blanks?: BlankInfo[];
  correctAnswer?: string[];
  explanation?: string;
}
```

### 8.3 课程组件类型

```typescript
// 课程组件摘要
interface CourseWidgetSummaryWithoutStatus {
  index: number;
  name: string;
  type: "exercise" | "guide" | "video" | "interactive";
  cdnUrl?: string;
}

// 课程组件数据
type CourseWidget<T extends string> = {
  index: number;
  name: string;
  type: T;
  data: T extends "exercise" ? ExerciseWidgetData :
        T extends "guide" ? GuideWidgetData :
        T extends "video" ? VideoWidgetData :
        T extends "interactive" ? InteractiveWidgetData : never;
}
```

## 9. 状态管理详解

### 9.1 Signal Store 架构

```typescript
// 基于 Preact Signals 的状态管理
class QuestionStore {
  // 核心状态
  questionStatus = signal<QuestionStatus>("loading");
  currentQuestion = signal<StudyQuestionInfo | null>(null);
  userAnswerDataMap = signal<Map<string, Answer[]>>(new Map());

  // UI 状态
  progressBarState = signal<ProgressBarState>({
    current: 0,
    total: 0,
    isVisible: true
  });

  timerState = signal<TimerState>({
    startTime: 0,
    currentTime: 0,
    isActive: false
  });

  transitionState = signal<TransitionState>({
    isVisible: false,
    type: "next",
    duration: 300
  });

  // 计算属性
  isAnswerComplete = computed(() => {
    const answers = this.getCurrentAnswers();
    return answers.every(answer => answer.content.trim() !== "");
  });

  // 状态更新方法
  updateUserAnswer = (params: UpdateStudentAnswerParams) => {
    // 实现答案更新逻辑
  };

  submitAnswer = async () => {
    // 实现答案提交逻辑
  };
}
```

### 9.2 Context 状态共享

```typescript
// 答题上下文
interface ExerciseContextType {
  questionStore: QuestionStore;
  studyType: StudyType;
  studySessionId: number;
  widgetIndex?: number;
  isPreview: boolean;
  clientContext: ClientContext;
  exerciseDisplayConfig?: ExerciseDisplayConfig;
}

// 课程视图上下文
interface CourseViewContextType {
  // 序列管理
  currentIndex: Signal<number>;
  total: number;
  widgetList: CourseWidgetSummaryWithoutStatus[];

  // 导航方法
  next: () => void;
  goto: (index: number) => void;
  exit: () => void;

  // 状态管理
  exerciseCompletedRecord: Signal<Record<number, boolean>>;
  isVersionChanged: Signal<boolean>;

  // 配置信息
  knowledgeId: number;
  lessonId: number;
  studySessionId: number;
  studyType: number;
}
```

## 10. 性能优化策略

### 10.1 组件级优化

```typescript
// 1. 条件渲染避免不必要的组件创建
const shouldLoad = useComputed(() => {
  return (
    index === currentIndex.value ||
    index === currentIndex.value + 1 ||
    index === currentIndex.value - 1
  );
});

// 2. useMemo 缓存复杂计算
const commonProps = useMemo(() => ({
  initialQuestionData: exerciseData.data,
  widgetIndex,
  studySessionId,
  // ... 其他属性
}), [exerciseData.data, widgetIndex, studySessionId]);

// 3. useCallback 缓存事件处理器
const handleComplete = useCallback((totalTimeSpent?: number) => {
  if (totalTimeSpent) {
    reportCostTime(totalTimeSpent);
  }
  next();
}, [next, reportCostTime]);
```

### 10.2 数据请求优化

```typescript
// SWR 配置优化
const swrOptions = summary?.type === "exercise" ? {
  revalidateOnFocus: false,
  revalidateOnReconnect: false,
  revalidateIfStale: false,
  refreshWhenOffline: false,
  refreshWhenHidden: false,
  dedupingInterval: 60000, // 60秒内相同请求去重
} : {};

// 条件请求避免无效调用
const { data, isLoading, error } = useCourseWidgetModel({
  knowledgeId,
  lessonId,
  summary: shouldLoad.value ? summary : undefined,
  nextQuestionParams,
});
```

### 10.3 内存管理

```typescript
// 1. 及时清理事件监听器
useEffect(() => {
  const unlisten = listenDeviceBackAction?.((result) => {
    if (result.code === 0 && result.data?.event === "backPressed") {
      handleExitRequest();
    }
  });

  return () => {
    unlisten?.(); // 清理监听器
  };
}, [handleExitRequest, listenDeviceBackAction]);

// 2. 使用 AbortController 取消请求
useEffect(() => {
  const controller = new AbortController();

  fetchData({ signal: controller.signal });

  return () => {
    controller.abort(); // 取消未完成的请求
  };
}, []);
```

## 11. 错误处理机制

### 11.1 分层错误处理

```typescript
// Model 层错误处理
export const useGetNextQuestion = (params: GetNextQuestionParams) => {
  return useSWR(
    getApiKey(params),
    async () => {
      try {
        const response = await api.getNextQuestion(params);
        return response.data;
      } catch (error) {
        // 统一错误处理
        throw new ExerciseError("获取题目失败", error);
      }
    }
  );
};

// ViewModel 层错误处理
export const useQuestionSubmissionVM = () => {
  const submitAnswer = async () => {
    try {
      setIsSubmitting(true);
      await questionStore.submitAnswer();
    } catch (error) {
      // 用户友好的错误提示
      showErrorToast("提交失败，请重试");
      console.error("Submit answer error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };
};

// View 层错误边界
const ErrorBoundary: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <ErrorBoundary
      fallback={<ErrorPage onRefresh={() => window.location.reload()} />}
    >
      {children}
    </ErrorBoundary>
  );
};
```

### 11.2 网络错误重试

```typescript
// 自动重试机制
const { data, error, mutate } = useSWR(
  apiKey,
  fetcher,
  {
    errorRetryCount: 3,
    errorRetryInterval: 1000,
    onError: (error) => {
      // 记录错误日志
      console.error("API Error:", error);

      // 特定错误处理
      if (error.status === 401) {
        // 重新登录
        redirectToLogin();
      }
    }
  }
);
```

## 12. 总结

答题组件架构采用现代化的 MVVM 模式和 Context-First 设计，实现了：

1. **清晰的分层**: View-ViewModel-Model 职责分离
2. **响应式状态**: 基于 Signals 的高效状态管理
3. **模块化设计**: 高内聚低耦合的组件结构
4. **多模式支持**: 灵活适配不同学习场景
5. **性能优化**: 多种优化策略保证流畅体验
6. **错误处理**: 完善的错误处理和恢复机制
7. **类型安全**: 完整的 TypeScript 类型定义

这种架构设计为答题系统提供了良好的可维护性、可扩展性和用户体验，同时确保了代码的健壮性和性能表现。
