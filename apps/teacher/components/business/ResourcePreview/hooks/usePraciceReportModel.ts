import { FEEDBACK_TYPE } from "@/enums";
import { FeedbackSource, useFeedbackByType } from "@/hooks/useReportFeedback";
import { QaContentType } from "@repo/core/views/tch-question-view/type";

export default function usePraciceReportModel(currentSubjectKey: number) {
    const { routeToFeedback } = useFeedbackByType();

    const onReport = (question: QaContentType) => {
        if (!question) return;
        routeToFeedback(FEEDBACK_TYPE.QUESTION, {
            feedbackSource: FeedbackSource.COURSE_PREVIEW,
            subjectId: currentSubjectKey,
            questionId: question.questionId,
            feedbackQuestionVersionId: question.questionVersionId,
            feedbackPhaseId: question.phase ?? -1,
        });
    };

    return {
        reportWidget: onReport,
    }
}