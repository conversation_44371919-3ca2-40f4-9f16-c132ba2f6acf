import { CommonQuestion } from "@repo/core/types";
import { QuestionRenderType } from "../enums";
import { Answer } from "../types/question";
import {
  getQuestionRenderType,
  isObjectiveQuestionType,
  isSelfEvaluationQuestionType,
  isSubjectiveQuestionType,
} from "./question-is";
import { getQuestionStemFillPointUuids } from "./questionBlank";

// 导入正确的接口
import { ItemStatus } from "../components/common/number-selector/enum";
import { IOption } from "../components/common/number-selector/types";
import { createEmptyAnswers } from "./question-answer";

/**
 * 判断题目是否需要显示Tab切换
 * 多空选择题（完形填空、七选五、多空单选、多空多选、多空判断）需要显示Tab
 *
 * @param question 题目数据
 * @returns boolean 是否需要显示Tab切换
 */
export function shouldShowAnswerTabs(question: CommonQuestion): boolean {
  const renderType = getQuestionRenderType(question);
  const answerOptionList = question?.questionAnswer?.answerOptionList;
  const isParentChildQuestion = renderType === QuestionRenderType.PARENT_CHILD;
  const isSubjectiveQuestion = isSubjectiveQuestionType(question);
  // 在非子母题的主观题情况下，如果一道题的答案数量大于1，则显示导航
  if (
    isSubjectiveQuestion &&
    (answerOptionList?.length || 0) > 1 &&
    !isParentChildQuestion
  ) {
    return true;
  }

  // 在子母题的情况下或者二维答案数组大于 1，说明有子题，需要显示导航
  const answerOptionMatrix = question?.questionAnswer?.answerOptionMatrix;
  return !!(
    (answerOptionMatrix && answerOptionMatrix.length > 1) ||
    isParentChildQuestion
  );
}

/**
 * 获取题目的答案选项矩阵信息
 * 用于Tab组件渲染不同的答案区域
 *
 * @param question 题目数据
 * @returns 答案选项矩阵或undefined
 */
export function getAnswerOptionMatrix(question: CommonQuestion) {
  return question?.questionAnswer?.answerOptionMatrix;
}

/**
 * 获取题目答案选项
 * @param question 题目数据
 * @param activeBlankIndex 当前激活的空索引
 * @returns 答案选项
 */
export function getAnswerOptionDataByBlankIndex(
  question: CommonQuestion,
  activeBlankIndex: number
) {
  const answerOptionMatrix = question?.questionContent?.questionOptionMatrix;
  const answerOption = answerOptionMatrix?.[activeBlankIndex] || [];
  if (!answerOption.length) {
    return question?.questionContent?.questionOptionList || [];
  }
  return answerOption || [];
}

/**
 * 子题元数据接口
 * 包含每个子题的静态结构信息，用于快速查找和状态判断
 */
export interface SubQuestionMeta {
  /** 题目在树结构中的路径索引 */
  path: number[];
  /** 题目渲染类型 */
  renderType: QuestionRenderType;
  /** 是否需要自评（主观题） */
  needSelfEval: boolean;
  /** 父题ID（如果是子题） */
  parentId?: string;
  /** 题目内容引用（用于快速访问） */
  questionRef: CommonQuestion;
}

/**
 * 构建题目结构的返回结果
 */
export interface QuestionStructureResult {
  /** 题目路径数组（用于导航） */
  questionPaths: number[][];
  /** 子题元数据映射表 */
  allQuestionMetaMap: Map<string, SubQuestionMeta>;
  /** 树选项数据（用于导航组件） */
  treeOptions: IOption[];
  /** 初始化的用户答案数据 */
  userAnswerDataMap: Map<string, Answer[]>;
}

/**
 * 纯函数：在一次遍历内构建题目结构数据（路径、元数据、树形选项、答案初始化）
 *
 * 功能概述
 * - 依据 rootQuestion（可能是无子题 / 一层子题 / 两层子题）构建：
 *   1) questionPaths：叶子题在树中的路径（简单题用 []，一层子题用 [i]，二层子题用 [i, j]）
 *   2) allQuestionMetaMap：以 questionId 为键的元数据（路径 / 渲染类型 / 自评标记 / 父子关系 / 问题引用）
 *   3) treeOptions：用于题目目录/导航的树形数据（简单题为每个填空点生成一项，一/二层子题按题结构生成）
 *   4) userAnswerDataMap：答案数据的初始化/补齐（不修改传入 Map；会基于其浅拷贝进行填充）
 *
 * 设计说明
 * - 有序性：Map 的遍历采用插入顺序（符合题目呈现顺序）
 * - 纯函数：不会修改传入的 existingUserAnswerDataMap，而是基于其内容创建新的 Map 并返回
 * - 两层深度：当前实现覆盖到“母题 → 子题 → 二级子题”两层，如以后出现更深层需改为递归
 * - 删除父级答案：当存在二级子题时，父级（子题）本身不再是叶子题，需删除其答案占位（仅保留其 children）
 *
 * 复杂度
 * - 时间：O(Q + B)，Q 为题目节点数（根/子/孙），B 为总空位数（在 createEmptyAnswers 内部与 stem 中空位数相关）
 * - 空间：O(Q + B)，返回的 Map/数组按题量与空位量线性增长
 *
 * @param rootQuestion 根题目数据；若为 null，返回空结构
 * @param existingUserAnswerDataMap 现有答案 Map（可选），用于在初始化时尽量复用用户已有输入
 * @returns QuestionStructureResult 包含 questionPaths / allQuestionMetaMap / treeOptions / userAnswerDataMap
 */
export function buildQuestionStructure(
  rootQuestion: CommonQuestion | null,
  existingUserAnswerDataMap?: Map<string, Answer[]>,
  isEnglish?: boolean
): QuestionStructureResult {
  // 叶子路径集合：如 [] / [i] / [i, j]
  const questionPaths: number[][] = [];
  // 题目元数据：按 questionId 索引（包含路径/渲染/自评/父Id/题目引用）
  const allQuestionMetaMap = new Map<string, SubQuestionMeta>();
  // 目录/导航用树形结构
  let treeOptions: IOption[] = [];
  // 基于外部传入的答案 Map 做浅拷贝，保持纯函数语义（不改传入对象引用）
  const userAnswerDataMap = new Map<string, Answer[]>(
    existingUserAnswerDataMap || new Map()
  );

  // 无题目：返回空结构，便于调用方直接解构使用
  if (!rootQuestion) {
    return {
      questionPaths,
      allQuestionMetaMap,
      treeOptions,
      userAnswerDataMap,
    };
  }

  // 子题列表：可能不存在或为空
  const subQuestionList = rootQuestion.subQuestionList || [];

  // =========================
  // 情况一：简单题（无子题）
  // =========================
  if (subQuestionList.length === 0) {
    // 判定渲染类型 & 是否需要自评（主观题）
    const renderType = getQuestionRenderType(rootQuestion);
    const needSelfEval = isSelfEvaluationQuestionType(rootQuestion);

    // 简单题路径以 [] 表示根节点自身为叶子
    questionPaths.push([]);

    // 记录根题的元信息（可用于快速定位 / 渲染选择）
    allQuestionMetaMap.set(rootQuestion.questionId, {
      path: [],
      renderType,
      needSelfEval,
      questionRef: rootQuestion, // 引用而非拷贝：不会扩大内存，但会延长对象生命周期
    });

    // 初始化该题的答案：会按题干里的“空位”个数补齐
    const existingAnswers =
      userAnswerDataMap.get(rootQuestion.questionId) || [];
    const emptyAnswers = createEmptyAnswers({
      stem: rootQuestion.questionContent?.questionStem ?? "",
      answers: existingAnswers,
      isEnglish: isEnglish ?? false,
      questionRenderType: renderType,
      isObjectiveQuestion: isObjectiveQuestionType(rootQuestion),
    });
    userAnswerDataMap.set(rootQuestion.questionId, emptyAnswers);

    // 目录项：简单题按“填空点”生成选项（如 1、2、3…）
    const fillPointUuids = getQuestionStemFillPointUuids(
      rootQuestion?.questionContent?.questionStem ?? ""
    );
    // 为每个填空点生成一个导航项（value 从 1 开始，便于展示）
    treeOptions = fillPointUuids.map((uuid, index) => {
      return {
        id: uuid,
        value: index + 1,
        status: ItemStatus.DEFAULT,
      };
    });
  } else {
    // =========================
    // 情况二：母子题（可能有一层或二层子题）
    // =========================
    subQuestionList.forEach((subQuestion, index) => {
      // 子题渲染/自评属性
      const renderType = getQuestionRenderType(subQuestion);
      const needSelfEval = isSelfEvaluationQuestionType(subQuestion);

      // 是否存在二级子题
      const hasGrandChildren = Boolean(
        subQuestion.subQuestionList && subQuestion.subQuestionList.length > 0
      );

      if (!hasGrandChildren) {
        // ---------- 仅一层子题 ----------
        // 路径形如 [index]
        const path = [index];
        questionPaths.push(path);

        // 子题元信息：父ID为根题
        allQuestionMetaMap.set(subQuestion.questionId, {
          path,
          renderType,
          needSelfEval,
          parentId: rootQuestion.questionId,
          questionRef: subQuestion,
        });

        // 初始化该子题的答案
        const existingAnswers =
          userAnswerDataMap.get(subQuestion.questionId) || [];
        const emptyAnswers = createEmptyAnswers({
          stem: subQuestion.questionContent?.questionStem ?? "",
          answers: existingAnswers,
          isEnglish: isEnglish ?? false,
          questionRenderType: renderType,
          isObjectiveQuestion: isObjectiveQuestionType(subQuestion),
        });
        userAnswerDataMap.set(subQuestion.questionId, emptyAnswers);

        // 目录项：一层子题直接作为叶子项（value 用序号，便于 UI 展示）
        treeOptions.push({
          id: subQuestion.questionId,
          value: index + 1,
          status: ItemStatus.DEFAULT,
          answer: [], // 叶子通常可携带答题摘要；此处占位为 []
        });
      } else {
        // ---------- 存在二级子题 ----------
        // 父级（当前 subQuestion）不再是叶子 → 清除其占位答案
        userAnswerDataMap.delete(subQuestion.questionId);

        const children: IOption[] = [];

        // 遍历二级子题
        subQuestion.subQuestionList!.forEach((grandChild, subIndex) => {
          const grandRenderType = getQuestionRenderType(grandChild);
          const grandNeedSelfEval = isSelfEvaluationQuestionType(grandChild);
          // 二级路径形如 [index, subIndex]
          const path = [index, subIndex];

          questionPaths.push(path);

          // 记录孙题（真正叶子）的元数据：父ID为 subQuestion
          allQuestionMetaMap.set(grandChild.questionId, {
            path,
            renderType: grandRenderType,
            needSelfEval: grandNeedSelfEval,
            parentId: subQuestion.questionId,
            questionRef: grandChild,
          });

          // 初始化该孙题的答案
          const existingAnswers =
            userAnswerDataMap.get(grandChild.questionId) || [];
          const emptyAnswers = createEmptyAnswers({
            stem: grandChild.questionContent?.questionStem ?? "",
            answers: existingAnswers,
            isEnglish: isEnglish ?? false,
            questionRenderType: grandRenderType,
            isObjectiveQuestion: isObjectiveQuestionType(grandChild),
          });
          userAnswerDataMap.set(grandChild.questionId, emptyAnswers);

          // 子级目录项（value 采用 "i.j" 形式，提升可读性）
          children.push({
            id: grandChild.questionId,
            value: `${index + 1}.${subIndex + 1}`,
            status: ItemStatus.DEFAULT,
            answer: [],
          });
        });

        // 父级目录项：自身不再是叶子，挂载 children
        treeOptions.push({
          id: subQuestion.questionId,
          value: index + 1,
          status: ItemStatus.DEFAULT,
          children,
        });
      }
    });
  }

  // 统一返回结构：供 VM / 视图层消费
  return { questionPaths, allQuestionMetaMap, treeOptions, userAnswerDataMap };
}

/**
 * 纯函数：检查题目组中是否存在需要自评的子题
 * 用于替代 submission-vm 中的 isSelfEval 判断
 *
 * @param allQuestionMetaMap 子题元数据映射表
 * @param isParentChild 是否为母子题
 * @param currentQuestionId 当前题目ID（用于非母子题场景）
 * @returns 是否存在需要自评的子题
 */
export function hasAnySelfEvalInWholeQuestionFn(
  allQuestionMetaMap: Map<string, SubQuestionMeta>,
  isParentChild: boolean,
  currentQuestionId?: string
): boolean {
  if (!isParentChild) {
    // 非母子题：直接检查当前题目
    if (!currentQuestionId) return false;
    const meta = allQuestionMetaMap.get(currentQuestionId);
    return meta?.needSelfEval ?? false;
  }

  // 母子题：检查组内是否有任何需要自评的子题
  for (const meta of allQuestionMetaMap.values()) {
    if (meta.needSelfEval) {
      return true;
    }
  }

  return false;
}

/**
 * 纯函数：获取题目组中所有需要自评的题目ID列表
 * 用于自评完成度检查
 *
 * @param allQuestionMetaMap 子题元数据映射表
 * @returns 需要自评的题目ID数组
 */
export function getSelfEvalQuestionIds(
  allQuestionMetaMap: Map<string, SubQuestionMeta>
): string[] {
  const selfEvalIds: string[] = [];

  for (const [questionId, meta] of allQuestionMetaMap.entries()) {
    if (meta.needSelfEval) {
      selfEvalIds.push(questionId);
    }
  }

  return selfEvalIds;
}

/**
 * 纯函数：根据题目ID获取其在路径数组中的索引
 * 用于导航定位
 *
 * @param allQuestionMetaMap 子题元数据映射表
 * @param questionPaths 题目路径数组
 * @param questionId 题目ID
 * @returns 路径索引，未找到返回 -1
 */
export function getQuestionPathIndex(
  allQuestionMetaMap: Map<string, SubQuestionMeta>,
  questionPaths: number[][],
  questionId: string
): number {
  const meta = allQuestionMetaMap.get(questionId);
  if (!meta) return -1;

  return questionPaths.findIndex(
    (path) =>
      path.length === meta.path.length &&
      path.every((index, i) => index === meta.path[i])
  );
}

/**
 * 获取第一个需要自评但尚未自评的空位位置。
 *
 * 查找逻辑：
 * 1. 按 questionMap 的插入顺序依次遍历所有题目。
 * 2. 跳过不需要自评 (needSelfEval = false) 的题目。
 * 3. 对需要自评的题目，遍历其所有答案：
 *    - 如果发现 selfEvaluation 为空 (null/undefined) 或 0（未作答），
 *      立即返回该题目的路径 (questionIndex) 与对应空位索引 (blankIndex)。
 * 4. 如果所有需要自评的空位都已完成自评，则返回
 *    最后一个需要自评的题目的最后一个空位位置。
 * 5. 如果根本没有需要自评的题目，则返回默认 { questionIndex: [], blankIndex: 0 }。
 *
 * 性能：
 * - 时间复杂度 O(Q + B)，其中 Q 为题目数，B 为总空位数。
 * - 空间复杂度 O(1)。
 * - 一旦遇到未自评的空位会立即返回，平均性能更优。
 *
 * @param answerMap  答案数据，key 为 questionId，value 为该题所有空位的 Answer[]
 * @param questionMap 题目元数据，key 为 questionId，value 为 SubQuestionMeta
 * @returns
 *   - 找到未自评空位：返回 { questionIndex, blankIndex }
 *   - 全部完成：返回最后一个自评题目的最后空位位置
 *   - 没有需要自评的题目：返回 { questionIndex: [], blankIndex: 0 }
 */
export const getFirstUnevaluatedBlank = (
  answerMap: Map<string, Answer[]>,
  questionMap: Map<string, SubQuestionMeta>
): { questionIndex: number[]; blankIndex: number } => {
  // 若不存在任何需要自评的题，回退到默认
  const fallback = { questionIndex: [] as number[], blankIndex: 0 };

  // 记录“最后一个需要自评的位置”
  let lastEvalPath: number[] | null = null;
  let lastEvalBlank = 0;

  for (const [questionId, meta] of questionMap) {
    if (!meta.needSelfEval) continue;

    const answers = answerMap.get(questionId) ?? [];
    const lastIndex = answers.length ? answers.length - 1 : 0;

    // 每遇到一个需要自评的题，都更新“最后位置”
    lastEvalPath = meta.path;
    lastEvalBlank = lastIndex;

    // 查找第一个“未自评”的空位
    for (let i = 0; i < answers.length; i++) {
      const se = answers[i]?.selfEvaluation;
      if (se == null || se === 0) {
        return { questionIndex: meta.path, blankIndex: i };
      }
    }
  }

  // 全部自评完成：如果存在需要自评的题，停在“最后一个自评位置”；否则用默认
  return lastEvalPath
    ? { questionIndex: lastEvalPath, blankIndex: lastEvalBlank }
    : fallback;
};

export interface IItem {
  id?: number | string;
  value?: number | string;
  status?: ItemStatus;
  answer?: number | string[];
  children?: IOption[];
}
interface IProps {
  options: IOption[];
  targetId?: IOption["id"];
  targetIdx?: number;
  item: IItem;
}
// 更新树形选项状态的递归函数
export const updateTreeOptions = (props: IProps): IOption[] => {
  const { options, targetId, targetIdx, item } = props;
  return options.map((option, index) => {
    if (option.id === targetId || index === targetIdx) {
      return { ...option, ...item };
    }
    if (option.children) {
      return {
        ...option,
        children: updateTreeOptions({
          options: option.children,
          targetId,
          targetIdx,
          item,
        }),
      };
    }
    return option;
  });
};

export const getTreeOptionForPath = (options: IOption[], path: number[]) => {
  let current: IOption | undefined;
  for (const idx of path) {
    current = options[idx];
    if (!current) return undefined;
    if (current.children) {
      options = current.children;
    }
  }
  return current;
};
