/**
 * 作答方式为填空题答题区内容展示，包含作答方式切换和答案回显
 */
import { useQuestionViewContext } from "@repo/core/new-exercise/context/question-view-context";
import { cn } from "@repo/ui/lib/utils";
import React, { useMemo } from "react";
import KeyboardInput from "../../components/answer-area/fill-blank/KeyboardInput";
import Whiteboard from "../../components/answer-area/fill-blank/Whiteboard";
import ImageUpload from "../../components/answer-area/fill-blank/image-upload";
import { useExerciseContext } from "../../context/exercise-context";
import { InputModeType } from "../../enums";
import { getWhiteBoardData } from "../../utils/question-answer";
import { useFillBlankQuestionViewModel } from "../../viewmodels/fill-blank-question-vm";
import { AnsweredSelfEvaluateView } from "./answered-self-evaluate-view";
export type BlankAnswer = {
  id: number;
  value: string;
};

export const FillBlankAnswerAreaView: React.FC = () => {
  // 🎯 只需要从统一Context获取题目数据
  const { questionStore, isPreview = false, studyType } = useExerciseContext();
  const { currentQuestion } = questionStore;
  const { whiteboardRef } = useQuestionViewContext();
  const { getImageData, handleImageChange, handleTextChange } =
    useFillBlankQuestionViewModel();
  const {
    activeBlankIndex,
    isAnswerCompletedStatus,
    currentInputMode,
    currentQuestionAnswers,
    userAnswerDataMap,
  } = questionStore;

  const currentWhiteBoardData = useMemo(() => {
    const data = getWhiteBoardData({
      questionId: currentQuestion.value?.questionId ?? "",
      blankIndex: activeBlankIndex.value,
      userAnswerDataMap: userAnswerDataMap.value,
    });
    // 如果当前不是白板模式，则返回空
    if (currentInputMode.value !== InputModeType.WHITEBOARD) return;
    return data?.paths;
  }, [
    userAnswerDataMap,
    currentInputMode.value,
    activeBlankIndex.value,
    currentQuestion.value?.questionId,
  ]);

  const currentAnswerContent = useMemo(
    () => currentQuestionAnswers.value?.[activeBlankIndex.value]?.content ?? "",
    [currentQuestionAnswers.value, activeBlankIndex.value]
  );

  return (
    <div
      className={cn(
        "flex-1 overflow-y-auto overflow-x-hidden"
        // !isAnswerCompletedStatus ? "h-auto" : "h-auto"
      )}
    >
      <div className="flex h-full flex-col">
        {/* 题目解析区域 */}
        {isAnswerCompletedStatus.value ? (
          <AnsweredSelfEvaluateView />
        ) : (
          <>
            {currentInputMode.value === InputModeType.KEYBOARD && (
              <KeyboardInput
                studyType={studyType}
                isPreview={isPreview}
                content={currentAnswerContent}
                handleTextChange={handleTextChange}
              />
            )}
            {currentInputMode.value === InputModeType.CAMERA && (
              <>
                <ImageUpload
                  signatureUrl="/api/v1/common/oss/token"
                  maxImages={3}
                  imageFiles={getImageData(activeBlankIndex.value) ?? []}
                  onImageFilesChange={handleImageChange}
                />
              </>
            )}
            {currentInputMode.value === InputModeType.WHITEBOARD && (
              <div className="mt-3 h-full w-full">
                <Whiteboard
                  ref={whiteboardRef}
                  paths={currentWhiteBoardData}
                  key={`${currentQuestion.value?.questionId}_${activeBlankIndex}`}
                  isPreview={isPreview}
                />
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};
