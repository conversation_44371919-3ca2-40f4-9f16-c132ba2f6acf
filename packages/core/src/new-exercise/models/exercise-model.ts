"use client";

import { StudyType } from "@repo/core/enums";

import useSWRMutation from "swr/mutation";
import { get, post } from "../utils/fetcher";
// ApiGetNextQuestionData 已从 types 导入
import { toast } from "@repo/core/components/stu-toast";
import * as Sentry from "@sentry/nextjs";
import { useCallback, useRef } from "react";
import {
  ApiGetNextQuestionData,
  SubmitAnswerParams,
  SubmitAnswerResponse,
} from "../types/question";

/**
 * 统一的错误处理方法
 *
 * 提取错误对象中的错误消息，支持多层嵌套的错误结构
 *
 * @param error 错误对象
 * @param defaultMessage 默认错误消息
 * @returns 提取的错误消息
 */

function handleApiError(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  error: any,
  defaultMessage: string = "网络请求失败"
): string {
  if (error?.message?.message && typeof error.message.message === "string") {
    return error.message.message;
  }
  if (error && typeof error.message === "string") {
    return error.message;
  }
  return defaultMessage;
}

/**
 * 提交题目答案并获取反馈
 *
 * 学生提交题目答案，系统判断答案正确性，记录作答详情，提供即时的情感化反馈。
 * 包含正确/错误/连胜状态反馈，并自动推进到下一题或完成练习。
 *
 * @returns SWR Mutation Hook 返回值
 */

// 你的其它 import：post / toast / handleApiError 等

export function useSubmitStudyAnswer() {
  // 冷却锁
  const coolingRef = useRef(false);

  const { trigger, isMutating, error } = useSWRMutation(
    "/api/v1/study_session/submit_answer",
    async (url, { arg }: { arg: SubmitAnswerParams }) => {
      try {
        const response = await post<SubmitAnswerResponse>(url, { arg });
        const transformedResponse = {
          ...response,
          questionInfo: {
            ...response.nextQuestionInfo,
            questionTags: response.nextQuestionTags || [],
          },
        } as SubmitAnswerResponse;
        return transformedResponse;
      } catch (err) {
        console.error("❌ [API错误] 提交失败:", err);
        toast.error(handleApiError(err));

        const error = new Error(
          err instanceof Error ? err.message : "P0: submitAnswer 提交接口失败"
        );

        error.name = "P0: submitAnswer 提交接口失败";

        Sentry.captureException(error, {
          level: "fatal",
          extra: {
            message: "P0: submitAnswer 提交接口失败",
            ...arg,
          },
        });
        throw err;
      }
    }
  );

  const submitAnswer = useCallback(
    async (arg: SubmitAnswerParams) => {
      if (coolingRef.current) {
        // 1s 窗口期内点击：直接提示，不发请求
        toast.info("正在提交～再点屏幕就要冒烟啦！");
        return;
      }

      coolingRef.current = true; // 进入冷却期
      setTimeout(() => {
        coolingRef.current = false;
      }, 1000); // 1s 后解锁

      return trigger(arg); // 真正发请求
    },
    [trigger]
  );

  return {
    submitAnswer,
    isSubmitting: isMutating,
    submitError: error,
  };
}

/**
 * 获取下一题（仅手动触发模式）
 *
 * 🔧 严格的手动触发模式：
 * - 绝对不会自动调用API
 * - 只返回传入的 firstQuestionData（初始题目）
 * - 所有后续题目通过 useSubmitStudyAnswer 获取
 *
 * @param params 配置参数
 * @returns 手动触发Hook返回值
 */
export function useGetNextQuestion(params: {
  firstQuestionData?: ApiGetNextQuestionData;
  studyType: StudyType;
  studySessionId: number;
  questionId?: string;
}) {
  // 🔥 关键：使用 useSWRMutation，但绝不自动触发
  const { trigger, isMutating, error } = useSWRMutation(
    "/api/v1/study_session/next_question",
    async (url, { arg }: { arg?: { questionId?: string } } = {}) => {
      // 🔧 判断是否需要发起请求
      const shouldFetch = params.studyType !== StudyType.AI_COURSE;

      if (!shouldFetch) {
        // AI课程不需要调用API，直接返回首题数据
        return params.firstQuestionData || null;
      }

      // 构建API参数
      const apiParams: Record<string, string> = {
        studySessionId: params.studySessionId.toString(),
        studyType: params.studyType.toString(),
        // 🔑 优先使用动态传递的 questionId，否则使用初始化时的 questionId
        questionId: arg?.questionId ?? params.questionId ?? "",
      };

      // 从URL中提取所有参数
      if (typeof window !== "undefined") {
        const urlParams = new URLSearchParams(window.location.search);
        urlParams.forEach((value, key) => {
          // 如果动态传递了 questionId，则不使用 URL 中的 questionId
          if (key === "questionId" && arg?.questionId) {
            return; // 跳过 URL 中的 questionId
          }
          apiParams[key] = value;
        });
      }

      try {
        const response = await get<ApiGetNextQuestionData>(url, {
          query: apiParams,
        });

        const transformedResponse = {
          ...response,
          questionInfo: {
            ...response.questionInfo,
            questionTags: response.questionTags || [],
          },
        };
        return transformedResponse as ApiGetNextQuestionData;
      } catch (error) {
        Sentry.captureException(error, {
          level: "fatal",
          extra: {
            初始数据: params.firstQuestionData,
            参数: apiParams,
          },
        });
        toast.error(handleApiError(error));
        throw error;
      }
    }
  );

  // 🔥 关键变更：只返回 firstQuestionData，绝不返回任何API获取的data
  // 这确保了只有初始数据会被使用，后续所有数据都来自submit接口
  return {
    questionInfo: params.firstQuestionData || null, // 🔥 移除 || data，确保不会使用API数据
    error: error,
    isLoading: isMutating,
    trigger, // �� 手动获取方法（备用）
    getNextQuestion: trigger, // 🔑 现在可以传递 { questionId: "xxx" } 参数
  };
}

/**
 * 退出练习会话请求参数
 */
export interface ExitSessionParams {
  studySessionId: number;
  questionId: string; // 当前题目ID，用于保存答题进度
  widgetIndex?: number; // 组件索引，用于标识当前练习组件位置
  answerDuration: number; // 当前答题时长
  /* 缓存当前答题内容 */
  cachedContent?: {
    // 当前用时
    timerState: {
      currentTime: number;
      totalTime: number;
    };
    // 当前题目ID
    questionId: string;
    userAnswerDataMap: Record<string, unknown>;
    activeBlankIndex: number;
    activeQuestionIndex: number[];
  };
}
/**
 * 退出练习会话
 *
 * 学生中途退出练习时调用此接口，系统自动保存当前进度。
 * 支持缓存和数据库双重保障，确保进度不丢失。
 *
 * @returns SWR Mutation Hook 返回值
 */
export function useExitStudySession() {
  const { trigger, isMutating, error } = useSWRMutation(
    "/api/v1/study_session/exit",
    async (url, { arg }: { arg: ExitSessionParams }) => {
      console.log("[useExitStudySession] 退出练习会话:", arg);
      let cachedContentString: string | undefined = undefined;
      try {
        cachedContentString = arg.cachedContent
          ? JSON.stringify(arg.cachedContent)
          : undefined;
      } catch (error) {
        console.error("[useExitStudySession] 缓存内容转换失败:", error);
        throw error;
      }

      // 退出接口使用 POST 方法，参数通过 body 传递
      const response = await post(url, {
        arg: {
          ...arg,
          cachedContent: cachedContentString,
        },
      });
      return response;
    }
  );

  return {
    exitSession: trigger,
    isExiting: isMutating,
    exitError: error,
  };
}

/**
 * 进入练习会话请求参数
 */
export interface EnterSessionParams {
  [key: string]: string;
}

/**
 * 进入练习会话响应
 */
export interface EnterSessionResponse {
  studySessionId?: number;
  [key: string]: unknown;
}

/**
 * 获取练习入口信息并自动创建/恢复会话
 *
 */
export function useEnterStudySession() {
  const { trigger, isMutating, error } = useSWRMutation(
    "/api/v1/study_session/enter",
    async (url, { arg }: { arg: EnterSessionParams }) => {
      // 进入练习前的会话
      const response = await get<EnterSessionResponse>(url, {
        query: arg,
      });
      return response;
    }
  );

  return {
    enterSession: trigger,
    isEntering: isMutating,
    enterError: error,
  };
}

/**
 * 校验图片答案
 *
 * 用于校验学生提交的图片答案是否合法,包括:
 * - 图片格式检查
 * - 图片内容审核
 * - 图片答案预判断
 *
 * @returns SWR Mutation Hook 返回值
 */
export interface CheckAnswerPictureRequest {
  pictureUrls: string[];
}

export interface CheckAnswerPictureResponse {
  isPass: boolean;
}

export function useCheckAnswerPicture() {
  const { trigger, isMutating, error } = useSWRMutation(
    "/api/v1/study_session/answer/picture/check",
    async (url, { arg }: { arg: CheckAnswerPictureRequest }) => {
      const response = await post<CheckAnswerPictureResponse>(url, { arg });
      return response;
    }
  );

  return {
    checkPicture: trigger,
    isChecking: isMutating,
    checkError: error,
  };
}

/**
 * 错题导航参数
 */
export interface WrongQuestionNavigationParams {
  studySessionId: number;
  questionId: string;
  studyType: StudyType;
}
