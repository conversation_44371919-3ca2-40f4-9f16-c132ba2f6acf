import { useSignal } from "@preact-signals/safe-react";
import * as Sentry from "@sentry/nextjs";
import { useCallback } from "react";
import { v4 as uuid } from "uuid";
import { useExerciseContext } from "../context/exercise-context";
import { QuestionViewContextType } from "../context/question-view-context";
import { InputModeType } from "../enums";
import { WhiteBoardData } from "../types";
import { retry, useImageUpload } from "./useImageUpload";

async function base64ToFile(dataUrl: string, fileName = "preview.webp") {
  const res = await fetch(dataUrl);
  const blob = await res.blob();
  return new File([blob], fileName, { type: blob.type });
}

/**
 * 白板答案管理 Hook - 重构版
 *
 * 功能：
 * 1. 直接操作 userAnswerDataMap，统一数据源
 * 2. 白板缓存数据存储在 Answer.whiteBoardData 中
 * 3. 上传成功后清除缓存，保留 content（图片URL）
 * 4. 消除数据重复和key拼接问题
 */
export function useWhiteboardAnswer({
  whiteboardRef,
  activeBlankIndex,
  inputMode,
}: {
  whiteboardRef: QuestionViewContextType["whiteboardRef"];
  activeBlankIndex: number;
  inputMode: InputModeType;
}) {
  const { questionStore } = useExerciseContext();
  const {
    currentQuestion,
    userAnswerDataMap,
    updateUserAnswer,
    currentInputMode,
  } = questionStore;
  // const { whiteboardRef, activeBlankIndex, inputMode } =
  //   useQuestionViewContext();

  // 使用现有的图片上传 hook
  const { clearUploadData, handleImageUpload } = useImageUpload({
    signatureUrl: "/api/v1/common/oss/token",
    isCheckPicture: false,
  });

  const isCheckingWhiteboardData = useSignal(false);

  const transAndUploadWhiteboardImage = useCallback(
    async (
      data: {
        questionId: string;
        blankIndex: number;
        type: InputModeType;
        whiteBoardData: WhiteBoardData;
      },
      isSync?: boolean
    ) => {
      const fileName = `whiteboard_preview_${uuid()}`;
      const imageFile = await base64ToFile(
        data.whiteBoardData?.imageData || "",
        `${fileName}.webp`
      );
      Sentry.addBreadcrumb({
        message: "转换白板数据并上传白板图片",
        data: {
          imageFile: imageFile,
          blankAnswerData: data,
        },
      });

      if (!imageFile) return;

      const doUpload = async () => {
        // 使用现有的图片上传方法
        const url = await handleImageUpload({
          id: fileName,
          file: imageFile,
          preview: "",
          status: "pending",
        });

        if (url) {
          console.log("上传白板图片成功", url);

          // 上传图片的过程中可能用户已经切换了模式，所以必须重新获取当前模式
          updateUserAnswer({
            ...data,
            type: currentInputMode.value,
            whiteBoardData: {
              ...data.whiteBoardData,
              paths: data.whiteBoardData?.paths || "[]",
              update: false,
              imageUrl: url,
            },
          });
          clearUploadData();

          return url;
        }
      };

      // 构造“带重试”的任务，并统一错误收敛，不抛出未处理拒绝
      const task = doUpload().catch((_err) =>
        retry(
          () => {
            Sentry.addBreadcrumb({
              message: "上传白板图片失败重试",
              data: { blankAnswerData: data },
            });
            return doUpload();
          },
          3,
          50
        ).catch((error) => {
          const msg = "白板图片上传失败";
          Sentry.captureException(error, {
            level: "fatal",
            extra: { message: msg, blankAnswerData: data },
          });
          return null; // 收敛失败为 null，避免未处理拒绝
        })
      );

      return isSync ? await task : task; // 非 sync 返回真实 Promise，给调用方等待
    },
    [currentInputMode, updateUserAnswer, clearUploadData, handleImageUpload]
  );

  // 保存白板数据（离开空时调用）
  const saveWhiteboardData = useCallback(
    async (type?: InputModeType, sync?: boolean) => {
      if (!whiteboardRef.current?.exportImage) return;
      const paths = await whiteboardRef.current?.exportPaths();

      if (!paths?.update) {
        return;
      }
      const image = await whiteboardRef.current?.exportImage();

      const isEmpty = paths?.paths === "[]";

      const answerData = {
        questionId: currentQuestion.value?.questionId || "",
        blankIndex: activeBlankIndex,
        type: type || InputModeType.WHITEBOARD,
        whiteBoardData: {
          update: true,
          paths: paths?.paths,
          imageData: image || "",
          imageUrl: "",
        },
      };

      if (isEmpty) {
        answerData.whiteBoardData.imageData = "";
        answerData.whiteBoardData.imageUrl = "";
        updateUserAnswer({
          ...answerData,
          whiteBoardData: {
            update: false,
            paths: "[]",
            imageData: "",
            imageUrl: "",
          },
        });
        return;
      }

      Sentry.addBreadcrumb({
        message: "保存白板数据",
        data: {
          answerData,
        },
      });

      updateUserAnswer(answerData);

      if (!answerData.whiteBoardData?.imageData) {
        return;
      }

      try {
        // 如果正在检查，则不进行上传
        if (isCheckingWhiteboardData.value) return;
        isCheckingWhiteboardData.value = true;

        const task = transAndUploadWhiteboardImage(answerData, sync);

        if (sync) {
          await task;
          isCheckingWhiteboardData.value = false;
        } else {
          task.finally(() => {
            isCheckingWhiteboardData.value = false;
          });
        }
      } catch (error) {
        console.error("保存白板数据失败:", error);
        Sentry.captureException(error, {
          level: "error",
          extra: { message: "保存白板数据失败", answerData },
        });
      } finally {
        isCheckingWhiteboardData.value = false;
      }
    },
    [
      transAndUploadWhiteboardImage,
      isCheckingWhiteboardData,
      currentQuestion.value,
      activeBlankIndex,
      whiteboardRef,
      updateUserAnswer,
    ]
  );

  // 提交前确保所有白板数据已上传 - 优化版：批量收集+并发上传
  const checkAllWhiteboardData = useCallback(async (): Promise<void> => {
    // 一次性收集所有需要上传的白板数据
    const pendingUploads: Array<{
      questionId: string;
      index: number;
      whiteBoardData: WhiteBoardData;
    }> = [];
    Sentry.addBreadcrumb({
      message: "检查所有白板数据是否需要上传",
      data: {
        pendingUploads,
      },
    });

    // 快速遍历收集
    for (const [questionId, blankDatas] of userAnswerDataMap.value) {
      blankDatas.forEach((answer, index) => {
        if (
          answer.type === InputModeType.WHITEBOARD &&
          answer.whiteBoardData &&
          !answer.whiteBoardData.imageUrl
        ) {
          pendingUploads.push({
            questionId,
            index,
            whiteBoardData: answer.whiteBoardData,
          });
        }
      });
    }

    // 如果没有待上传项，直接返回
    if (pendingUploads.length === 0) return;

    // 批量并发上传（限制并发数避免服务器压力）
    const CONCURRENT_LIMIT = 3;
    const uploadPromises: Promise<unknown>[] = [];

    for (let i = 0; i < pendingUploads.length; i += CONCURRENT_LIMIT) {
      const batch = pendingUploads.slice(i, i + CONCURRENT_LIMIT);
      const batchPromises = batch.map((upload) =>
        transAndUploadWhiteboardImage(
          {
            questionId: upload.questionId,
            blankIndex: upload.index,
            type: InputModeType.WHITEBOARD,
            whiteBoardData: upload.whiteBoardData,
          },
          false
        )
      );
      uploadPromises.push(...batchPromises);
    }

    // 等待所有上传完成（或失败）
    await Promise.allSettled(uploadPromises);
  }, [userAnswerDataMap, transAndUploadWhiteboardImage]);

  return {
    // 业务逻辑
    saveWhiteboardData,
    checkAllWhiteboardData,
  };
}
