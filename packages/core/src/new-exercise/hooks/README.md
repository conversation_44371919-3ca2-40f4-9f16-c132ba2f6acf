# Hooks 使用指南

## 概述
本目录包含新版答题系统的各种自定义 Hooks，提供可复用的业务逻辑和状态管理。

## 核心 Hooks

### useWhiteboardAnswer
白板答案管理 Hook，专门处理白板作答的数据保存、上传和同步。

#### 功能特性
- **数据管理**：白板数据的增删改查
- **变化检测**：自动检测白板数据是否变化
- **异步上传**：离开空时自动上传，不阻塞 UI
- **提交保障**：提交前确保所有白板数据已上传
- **状态同步**：与统一答案状态自动同步

#### 使用方式

```typescript
import { useWhiteboardAnswer } from '@repo/core/new-exercise/hooks';

function WhiteboardComponent() {
  const {
    saveWhiteboardData,
    checkAllWhiteboardData,
    clearWhiteBoardData
  } = useWhiteboardAnswer();

  // 离开空时保存数据
  useEffect(() => {
    return () => {
      if (currentQuestionId && currentBlankIndex !== null && whiteboardData) {
        saveWhiteboardData(currentQuestionId, currentBlankIndex, whiteboardData);
      }
    };
  }, [currentQuestionId, currentBlankIndex, whiteboardData]);

}
```

#### API 参考

**数据管理方法**
- `updateWhiteBoardData(data: WhiteBoardData)`: 更新白板数据
- `clearWhiteBoardData(questionId?: string)`: 清空白板数据
- `getQuestionWhiteboardData(questionId: string)`: 获取题目的所有白板数据

**业务逻辑方法**
- `saveWhiteboardData(questionId, index, whiteBoardData)`: 保存白板数据并异步上传
- `checkAllWhiteboardData()`: 确保所有白板数据已上传（提交前调用）
- `uploadWhiteboardImage(questionId, index, whiteBoardData)`: 手动上传白板图片

#### 集成点

**1. 白板组件离开时**
```typescript
// 在白板组件的 useEffect 清理函数中
useEffect(() => {
  return () => {
    saveWhiteboardData(currentQuestionId, currentBlankIndex, whiteboardData);
  };
}, [currentQuestionId, currentBlankIndex, whiteboardData]);
```

**2. 提交前检查**
```typescript
// 在 submission-vm.ts 的 beforeSubmitAnswer 中
const { checkAllWhiteboardData } = useWhiteboardAnswer();
await checkAllWhiteboardData();
```

#### 数据流程
1. 用户在白板上绘画 → 白板组件更新 `whiteBoardData`
2. 离开空时 → 调用 `saveWhiteboardData` → 检查变化 → 异步上传
3. 上传成功 → 更新 `content` 字段 → 同步到 `userAnswerDataMap`
4. 提交前 → 调用 `checkAllWhiteboardData` → 重试失败的上传

#### 注意事项
- 白板转图片方法需要白板组件提供，当前使用占位实现
- 上传失败不会阻塞用户操作，提交时会重试
- 数据变化检测使用 JSON 字符串比较，适用于大多数场景
- 所有方法都是异步安全的，可以在任何时候调用

### 其他 Hooks

#### useClient
客户端环境检测和方法注入

#### useQuestionStem  
题干内容处理和渲染


#### useWrongQuestionBook
错题本功能集成

## 最佳实践

1. **Hook 组合使用**：多个 Hook 可以在同一组件中使用，互不冲突
2. **错误处理**：所有 Hook 都包含错误处理，不会导致应用崩溃
3. **性能优化**：使用 useCallback 和 useMemo 优化性能
4. **类型安全**：所有 Hook 都提供完整的 TypeScript 类型定义

## 扩展指南

添加新的 Hook 时：
1. 在 hooks 目录下创建新文件
2. 遵循现有的命名规范（use-xxx-xxx.ts）
3. 在 index.ts 中导出
4. 更新本 README 文档
5. 编写单元测试（推荐）
