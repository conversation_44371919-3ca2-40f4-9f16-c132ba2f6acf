/**
 * 简化的性能优化工具 - 为现有 Context 提供 useSignal 支持
 *
 * 设计理念：
 * - 不替换现有架构，只提供性能优化
 * - 专注解决计时器、进度条等高频更新场景
 * - 保持向后兼容，渐进式优化
 */

"use client";

import { batch, useComputed, useSignal } from "@preact-signals/safe-react";
import { ProgressBarProps } from "../components";
import { ConfirmDialogConfig } from "../components/exercise-dialog";
import { InputModeType, QuestionRenderType, SelfEvaluateType } from "../enums";
import { getStrategyForQuestion } from "../strategies";
import type { ExerciseInitParams } from "../types/exercise-config";
import type {
  Answer,
  AnswerResult,
  ApiGetNextQuestionData,
  QuestionStatus,
  StudentAnsweredInfo,
  StudyQuestionInfo,
  SubmitAnswerResponse,
  UpdateStudentAnswerParams,
} from "../types/question";
import {
  getQuestionRenderType,
  isChoiceQuestionType,
  isMultipleChoiceQuestionType,
  isObjectiveQuestionType,
  isSelfEvaluationQuestionType,
  isSubjectiveQuestionType,
  isSystemGradedQuestionType,
} from "../utils/question-is";

import { toast } from "@repo/core/components/stu-toast";
import { StudyType } from "@repo/core/enums";
import { SUBJECT } from "@repo/core/enums/lesson";
import * as Sentry from "@sentry/nextjs";
import { getInputModeOptions } from "../components/answer-area/InputModeSwitcher";
import {
  buildInitAnswerData,
  getAnswerContentWithInputMode,
  hasPendingSelfEvaluation,
} from "../utils/question-answer";
import {
  getFirstUnevaluatedBlank,
  hasAnySelfEvalInWholeQuestionFn,
  type SubQuestionMeta,
} from "../utils/question-structure";
import { TransitionState } from "../viewmodels/animation-transition-vm";
import usePreviewStore from "./preview-store";

// ===== 🔧 类型定义 =====

/** 🎯 不再需要 QuestionBaseConfigState，直接使用 ExerciseInitParams */

export type QuestionStoreType = ReturnType<typeof useQuestionStore>;

export const useQuestionStore = (exerciseParams: ExerciseInitParams) => {
  /** 🎯 直接使用 exerciseParams，不再需要重复声明 */

  const exerciseDisplayConfig = exerciseParams.displayConfig;

  /* 初始化题目数据 */
  const initialQuestionData = useSignal<ApiGetNextQuestionData | null>(null);

  const initialProgress = useComputed(() => {
    return initialQuestionData.value?.progressInfo?.currentProgress || 0;
  });

  /* 上一次提交结果 */
  const lastSubmitResult = useSignal<SubmitAnswerResponse | null>(null);

  // ===== 🔧 弹窗状态 =====
  const confirmDialogState = useSignal<ConfirmDialogConfig>({
    isVisible: false,
  });

  const isLoading = useSignal(false);
  /* 作答期间的状态 */
  const isSubmitting = useSignal(false);
  const streakCount = useSignal(0);
  const questionStatus = useSignal<QuestionStatus>("answering");
  // 根级题目
  const rootQuestion = useSignal<StudyQuestionInfo | null>(null);

  const isAiCourseResume = useComputed(() => {
    return (
      exerciseParams.studyType === StudyType.AI_COURSE &&
      initialQuestionData.value?.studentAnswer?.questionId ===
        initialQuestionData.value?.questionInfo?.questionId
    );
  });

  const subjectId = useComputed(() => {
    return initialQuestionData.value?.questionInfo?.subjectId;
  });

  const isEnglish = useComputed(() => {
    return subjectId.value === SUBJECT.SUBJECT_ENGLISH;
  });

  // 当前激活的子题，如果不是母子题，则和根级题目相同

  /* 计时器相关状态 - 统一管理 */
  const timerState = useSignal({
    currentTime: 0, // 当前题目计时（毫秒）
    totalTime: 0, // 总计时（毫秒）
  });

  /* 转场动画状态 */
  const transitionState = useSignal<TransitionState>({
    queue: {
      items: [],
      currentIndex: -1,
      totalDuration: 0,
    },
    currentTransition: null,
    isPlayingTransitions: false,
  });

  const progressBarState = useSignal<ProgressBarProps>({
    progress: 0,
    activeType: "static",
  });

  /** 统一答案状态管理  */
  const userAnswerDataMap = useSignal<Map<string, Answer[]>>(new Map());

  /** 🆕 子题元数据映射表 - 静态结构信息 */
  const allQuestionMetaMap = useSignal<Map<string, SubQuestionMeta>>(new Map());

  /* 打平的判题结果 */
  const answerVerifMap = useSignal<Map<string, AnswerResult>>(new Map());
  const updateAnswerVerifMap = (list: AnswerResult[]) => {
    const next = new Map(answerVerifMap.value);
    list.forEach((r) => next.set(r.questionId, r));
    answerVerifMap.value = next; // 关键：新引用
  };

  // 导航相关 signal
  const activeBlankIndex = useSignal(0);
  const activeQuestionIndex = useSignal<number[]>([]);

  const currentInputMode = useComputed(() => {
    if (!currentQuestion.value) {
      // 英语解答题默认使用键盘方式
      return getInputModeOptions(
        isEnglish.value,
        questionRenderType.value === QuestionRenderType.QA
      )?.[0]?.value;
    }
    const type =
      currentQuestionAnswers.value?.[0]?.type || InputModeType.WHITEBOARD;
    return type;
  });
  const isShowExitConfirm = useSignal(false);
  const isExiting = useSignal(false);
  // 是否已经作答完了
  const isAnswerCompletedStatus = useComputed(() => {
    return (
      questionStatus.value === "submitted" ||
      questionStatus.value === "evaluating" ||
      questionStatus.value === "giving_up"
    );
  });
  // 该用computed，避免重复计算
  const userAnswerTypeMap = useComputed(() => {
    const map = new Map<string, InputModeType>();
    userAnswerDataMap.value.forEach((answers, questionId) => {
      map.set(questionId, answers[0]?.type || InputModeType.KEYBOARD);
    });
    return map;
  });

  const currentQuestion = useComputed(() => {
    const question = rootQuestion.value;
    if (!question) return null;

    // 非母子题：直接返回根题
    if (!question.subQuestionList || question.subQuestionList.length === 0) {
      return question;
    }

    // 🔑 特殊处理：如果 activeQuestionIndex 包含 -1，说明是错题导航，返回根题
    if (activeQuestionIndex.value.includes(-1)) {
      return question;
    }

    // 母子题：根据 activeQuestionIndex 定位子题
    let current = question;
    for (const idx of activeQuestionIndex.value) {
      if (current.subQuestionList && current.subQuestionList[idx]) {
        current = current.subQuestionList[idx];
      } else {
        // 如果路径无效，返回根题
        return question;
      }
    }

    return current;
  });

  const updateProgressBarState = (state: Partial<ProgressBarProps>) => {
    progressBarState.value = {
      ...progressBarState.value,
      ...state,
    };
  };

  const navigationNextEvaluatingQuestionBlank = () => {
    const { questionIndex, blankIndex } = getFirstUnevaluatedBlank(
      userAnswerDataMap.value,
      allQuestionMetaMap.value
    );

    activeQuestionIndex.value = questionIndex;
    activeBlankIndex.value = blankIndex;
  };

  const updateQuestionStatus = (state: Partial<QuestionStatus>) => {
    questionStatus.value = state;
    if (state === "second_answering") {
      clearUserAnswer();
    }

    if (state === "evaluating") {
      navigationNextEvaluatingQuestionBlank();
    }
  };

  const resetCurrentQuestionTime = () => {
    timerState.value = {
      ...timerState.value,
      currentTime: 0,
    };
  };

  /* 转场动画控制方法 */
  const updateTransitionState = (state: Partial<TransitionState>) => {
    transitionState.value = {
      ...transitionState.value,
      ...state,
    };
  };

  const updateUserAnswerType = (questionId: string, type: InputModeType) => {
    const answers = userAnswerDataMap.value.get(questionId);
    if (!answers) return;

    const newAnswers = answers.map((item) => ({
      ...item,
      type,
    }));
    // 必须声明一个新的 map 重新赋值，否则不生效
    const newMap = new Map(userAnswerDataMap.value);
    newMap.set(questionId, newAnswers);
    userAnswerDataMap.value = newMap;
  };

  const updateUserAnswer = (state: UpdateStudentAnswerParams) => {
    const {
      questionId,
      blankIndex,
      type,
      content,
      whiteBoardData,
      cameraData,
    } = state;

    updateUserAnswerType(questionId, type);
    // 如果当前题目状态为放弃作答或不确定，则更新题目状态为作答中
    if (questionStatus.value === "uncertain") {
      updateQuestionStatus("answering");
    }

    // 如果已经作答完了，则不更新答案
    if (isAnswerCompletedStatus.value) {
      return;
    }

    const newAnswerData = new Map(userAnswerDataMap.value);

    // 🔧 修复：正确处理答案更新逻辑
    const cloneCurrentAnswers = [...(newAnswerData.get(questionId) || [])];
    const currentBlankAnswer = cloneCurrentAnswers[blankIndex];

    // 如果当前是键盘模式，则使用 content，否则使用 currentBlankAnswer 的 content
    const _content =
      type === InputModeType.KEYBOARD ? content : currentBlankAnswer?.content;
    const newAnswer: Answer = {
      index: blankIndex,
      type,
      content: _content || "",
      whiteBoardData: whiteBoardData || currentBlankAnswer?.whiteBoardData,
      cameraData: cameraData || currentBlankAnswer?.cameraData,
    };

    const validContent = getAnswerContentWithInputMode(newAnswer);
    // 如果没有获取到有效答案，则设置自评为错误
    if (!validContent) {
      newAnswer.selfEvaluation = SelfEvaluateType.wrong;
    }

    // if (type === InputModeType.WHITEBOARD && whiteBoardData?.paths === "{}") {
    //   newAnswer.whiteBoardData = {
    //     update: false,
    //     paths: "{}",
    //     imageData: "",
    //     imageUrl: "",
    //   };
    // }

    // if (type === InputModeType.CAMERA && !cameraData?.length) {
    //   newAnswer.cameraData = [];
    // }

    cloneCurrentAnswers[blankIndex] = newAnswer;

    newAnswerData.set(questionId, cloneCurrentAnswers);

    console.log("updateUserAnswer", {
      state,
      newAnswerData,
      cloneCurrentAnswers,
    });

    userAnswerDataMap.value = newAnswerData;
  };

  /** 🔥 新增：清空当前题目的用户答案 */

  const clearUserAnswer = (questionId?: string) => {
    const currentId = rootQuestion.value?.questionId;
    const targetId = questionId || currentId;

    if (!targetId) {
      console.warn("[clearUserAnswer] 当前没有题目，无法清空答案");
      return;
    }

    const reset = (a: Answer) => {
      a.content = "";
      a.whiteBoardData = undefined;
      a.cameraData = undefined;
    };

    const map = new Map(userAnswerDataMap.value);

    if (targetId === currentId) {
      // 清空所有题目的答案
      for (const answers of map.values()) {
        for (const ans of answers) reset(ans);
      }
    } else {
      // 仅清空指定题目的答案
      const answers = map.get(targetId);
      if (!answers) return;
      for (const ans of answers) reset(ans);
    }

    // 触发响应（浅拷贝一份 Map）
    userAnswerDataMap.value = map;
  };

  /** 统一自评更新方法 */
  const updateSelfEvaluation = (state: {
    questionId: string;
    blankIndex: number;
    selfEvaluation: SelfEvaluateType;
  }) => {
    const { questionId, blankIndex, selfEvaluation } = state;
    const currentAnswers = new Map(userAnswerDataMap.value);
    const questionAnswers = currentAnswers.get(questionId) || [];

    // 更新指定索引的自评结果
    const updatedAnswers = questionAnswers.map((answer) =>
      answer.index === blankIndex ? { ...answer, selfEvaluation } : answer
    );

    currentAnswers.set(questionId, updatedAnswers);

    userAnswerDataMap.value = currentAnswers;
  };

  const currentQuestionAnswers = useComputed(() =>
    userAnswerDataMap.value?.get(currentQuestion.value?.questionId || "")
  );

  // 每个题目的空数量
  const currentQuestionBlankCount = useComputed(() => {
    return currentQuestionAnswers.value?.length || 1;
  });

  // ===== 🔧 计算属性 =====

  /* 题目类型 */
  const rootQuestionRenderType = useComputed(() => {
    if (!rootQuestion.value) return null; // 返回 null 而不是空字符串
    return getQuestionRenderType(rootQuestion.value);
  });
  const questionRenderType = useComputed(() => {
    if (!currentQuestion.value) return null; // 返回 null 而不是空字符串
    return getQuestionRenderType(currentQuestion.value);
  });

  /* 当前的题目策略 */
  const rootQuestionStrategy = useComputed(() => {
    if (!rootQuestion.value) return null;
    return getStrategyForQuestion(rootQuestion.value);
  });

  /* 当前的题目策略 */
  const currentStrategy = useComputed(() => {
    if (!rootQuestion.value) return null;
    return getStrategyForQuestion(rootQuestion.value);
  });

  // 是否为主观题
  const isSubjectiveQuestion = useComputed(() => {
    if (!currentQuestion.value) return false;
    return isSubjectiveQuestionType(currentQuestion.value);
  });

  // 是否为客观题
  const isObjectiveQuestion = useComputed(() => {
    if (!currentQuestion.value) return false;
    return isObjectiveQuestionType(currentQuestion.value);
  });

  // 是否为系统判题
  const isSystemGradedQuestion = useComputed(() => {
    if (!currentQuestion.value) return false;
    return isSystemGradedQuestionType(currentQuestion.value);
  });

  // 是否为自评题
  const isSelfEvaluationQuestion = useComputed(() => {
    if (!currentQuestion.value) return false;
    return isSelfEvaluationQuestionType(currentQuestion.value);
  });

  // 是否为选择题
  const isChoiceQuestion = useComputed(() => {
    if (!currentQuestion.value) return false;
    return isChoiceQuestionType(currentQuestion.value);
  });

  // 是否为多选题
  const isMultipleChoiceQuestion = useComputed(() => {
    if (!currentQuestion.value) return false;
    return isMultipleChoiceQuestionType(currentQuestion.value);
  });

  const isParentChildQuestion = useComputed(() => {
    if (!rootQuestion.value) return false;
    return rootQuestionRenderType.value == QuestionRenderType.PARENT_CHILD;
  });

  // 🆕 当前题目组中是否存在需要自评的子题
  const hasAnySelfEvalInWholeQuestion = useComputed(() => {
    return hasAnySelfEvalInWholeQuestionFn(
      allQuestionMetaMap.value,
      isParentChildQuestion.value,
      currentQuestion.value?.questionId
    );
  });

  // 是否可以提交自评，所有空都完成了自评
  const canSubmitSelfEvaluation = useComputed(() => {
    let canSubmit = true;
    // 遍历检查userAnswerData中每一道子题的每一个selfEvaluation是否有值

    for (const meta of allQuestionMetaMap.value.values()) {
      if (!meta) continue;
      const selfEvalResult = userAnswerDataMap.value.get(
        meta.questionRef.questionId
      );

      const hasPendingSelfEvaluationState = hasPendingSelfEvaluation(
        selfEvalResult || []
      );

      // 如果子题需要自评但还有值为空，则不能提交自评
      if (meta.needSelfEval && hasPendingSelfEvaluationState) {
        canSubmit = false;
        break;
      }
    }

    return canSubmit;
  });

  const initUserAnswerDataMap = (
    studentAnsweredInfo: StudentAnsweredInfo | null,
    isSubjectiveQuestion: boolean,
    isAiCourseResume?: boolean
  ) => {
    const { answerContents, answerDuration, answerResult } =
      studentAnsweredInfo || {};
    timerState.value.currentTime = answerDuration || 0;
    let _hasIncompleteSelfEvaluation = true;

    // 否则应该保持 answering 状态，等待用户主动提交答案
    if (answerContents) {
      // 判题结果映射自评

      const { initializedAnswerData, hasIncompleteSelfEvaluation } =
        buildInitAnswerData({ answerContents, isAiCourseResume });

      console.log(`initializedAnswerData`, initializedAnswerData);
      _hasIncompleteSelfEvaluation = hasIncompleteSelfEvaluation;
      userAnswerDataMap.value = initializedAnswerData;

      if (exerciseParams.isPreview) return;

      if (isSubjectiveQuestion) {
        if (!hasIncompleteSelfEvaluation) {
          // 已经完成自评，设置为 evaluating 状态
          updateQuestionStatus("submitted");
        } else {
          // 主观题，直接设置为 evaluating 状态
          updateQuestionStatus("evaluating");
        }
      } else {
        updateQuestionStatus("submitted");
      }
    }
  };

  // 初始化题目数据和状态
  const initialQuestionInfo = (
    _initialQuestionData: ApiGetNextQuestionData | null
  ) => {
    // 🔧 添加面包屑
    Sentry.addBreadcrumb({
      category: "initialQuestionInfo",
      message: "初始化题目数据",
      data: {
        _initialQuestionData,
      },
    });

    initialQuestionData.value = _initialQuestionData;

    if (!initialQuestionData.value?.questionInfo) {
      throw new Error("initialQuestionData.value?.questionInfo is null");
    }

    // 如果题目相同，则不进行初始化
    if (
      _initialQuestionData?.questionInfo?.questionId ==
      rootQuestion.value?.questionId
    ) {
      return;
    }
    // 🔑 确保 wrongQuestionInfo 和 exerciseStats 从 ApiGetNextQuestionData 传递到 StudyQuestionInfo
    const questionInfo = initialQuestionData.value?.questionInfo;
    if (questionInfo) {
      // 使用 batch 避免循环依赖
      batch(() => {
        rootQuestion.value = {
          ...questionInfo,
          // 将顶层的 wrongQuestionInfo 传递到 questionInfo 中
          wrongQuestionInfo:
            initialQuestionData.value?.wrongQuestionInfo ||
            questionInfo.wrongQuestionInfo,
          // 🔧 将顶层的 exerciseStats 传递到 questionInfo 中
          exerciseStats:
            initialQuestionData.value?.exerciseStats ||
            questionInfo.exerciseStats,
        };
      });

      // 🆕 调用 onQuestionChange 回调，通知外部题目已初始化
      if (exerciseParams.onQuestionChange && rootQuestion.value) {
        exerciseParams.onQuestionChange({
          questionData: rootQuestion.value,
          index: 0, // 初始化时索引为 0
          questionStatus: questionStatus.value,
          questionType: {
            isObjective: isObjectiveQuestionType(rootQuestion.value),
            isSubjective: isSubjectiveQuestionType(rootQuestion.value),
            isChoice: isChoiceQuestionType(rootQuestion.value),
            isMultipleChoice: isMultipleChoiceQuestionType(rootQuestion.value),
            isSelfEvaluation: isSelfEvaluationQuestionType(rootQuestion.value),
          },
        });
      }
    } else {
      rootQuestion.value = null;
    }

    const _isSubjectiveQuestion = isSubjectiveQuestionType(
      initialQuestionData.value?.questionInfo
    );
    let _cacheData;
    try {
      if (_initialQuestionData?.cachedContent) {
        _cacheData = JSON.parse(_initialQuestionData?.cachedContent);
      }
    } catch (error) {
      console.error("initialQuestionInfo cacheContent parse error", error);
    }

    if (_cacheData) {
      batch(() => {
        userAnswerDataMap.value = new Map(
          Object.entries(_cacheData.userAnswerDataMap)
        );

        console.log("initialQuestionInfo", _cacheData);

        activeBlankIndex.value = _cacheData.activeBlankIndex;
        activeQuestionIndex.value = _cacheData.activeQuestionIndex;
        timerState.value = _cacheData.timerState;
        questionStatus.value = _cacheData.questionStatus;
      });
    } else {
      resetQuestionStateOnNext();

      initUserAnswerDataMap(
        initialQuestionData.value?.studentAnswer || null,
        _isSubjectiveQuestion,
        isAiCourseResume.value
      );
    }
  };

  // ===== 🔧 工具函数 =====

  /** 🎯 不再需要 initializeConfig 和 resetuseSignals，参数通过 exerciseParams 传入 */

  /**
   * 重置业务状态（保留必要的重置逻辑）
   * !!!使用该方法需要极其小心，会清楚所有状态，包括作答数据、questionStatus等  */
  function resetQuestionStateOnNext(params?: {
    time?: number;
    status?: QuestionStatus;
  }) {
    const { time, status } = params || {};
    batch(() => {
      isSubmitting.value = false;
      streakCount.value = 0;
      questionStatus.value = status || "answering";

      timerState.value = {
        currentTime: time || 0,
        totalTime: timerState.value.totalTime,
      };
      // initialQuestionData.value = null;
      activeBlankIndex.value = 0;
      activeQuestionIndex.value = [];

      // 🔥 重置新增的状态
      userAnswerDataMap.value = new Map();
    });
  }

  const hasNextQuestion = useComputed(() => {
    return (
      lastSubmitResult.value?.hasNextQuestion ??
      initialQuestionData.value?.hasNextQuestion ??
      false
    );
  });

  const handleNextQuestion = () => {
    batch(() => {
      if (!lastSubmitResult.value) {
        const err = new Error(
          "P0: handleNextQuestion 没有 lastSubmitResult! 请检查后端数据!"
        );
        err.name = "P0：用户点击继续按钮卡住！！！";
        Sentry.captureException(err, {
          level: "fatal",
          extra: {
            initialQuestionData: initialQuestionData.value,
            currentQuestion: currentQuestion.value,
            lastSubmitResult: lastSubmitResult.value,
            userAnswerDataMap: Object.fromEntries(userAnswerDataMap.value),
            allQuestionMetaMap: Object.fromEntries(allQuestionMetaMap.value),
          },
        });
        toast.error("系统开小差了，请稍后再试");
        return;
      }
      // 如果 hasNextQuestion 为 true 但 nextQuestionInfo 为空，则抛出错误
      if (
        lastSubmitResult.value?.hasNextQuestion &&
        !lastSubmitResult.value?.nextQuestionInfo
      ) {
        const err = new Error(
          "P0: hasNextQuestion 为 true, 但 nextQuestionInfo 为空! 请检查后端数据!"
        );
        err.name = "P0：用户点击继续按钮卡住！！！";

        // toast.error(errorMessage);
        Sentry.captureException(err, {
          level: "fatal",
          extra: {
            initialQuestionData: initialQuestionData.value,
            currentQuestion: currentQuestion.value,
            lastSubmitResult: lastSubmitResult.value,
            userAnswerDataMap: Object.fromEntries(userAnswerDataMap.value),
            allQuestionMetaMap: Object.fromEntries(allQuestionMetaMap.value),
          },
        });

        toast.error("系统开小差了，请稍后再试");

        throw err;
      }

      resetQuestionStateOnNext({
        status: "answering",
      });

      // 🔧 设置下一题，并将 SubmitAnswerResponse.exerciseStats 应用到下一题
      const nextQuestion = lastSubmitResult.value?.nextQuestionInfo ?? null;
      const nextQuestionExerciseStats = lastSubmitResult.value?.exerciseStats;

      if (nextQuestion && nextQuestionExerciseStats) {
        // 将下一题的统计信息合并到题目信息中
        rootQuestion.value = {
          ...nextQuestion,
          exerciseStats: nextQuestionExerciseStats,
        };
      } else {
        rootQuestion.value = nextQuestion;
      }
    });
  };

  const previewStore = usePreviewStore({
    exerciseParams,
    rootQuestion,
    progressBarState,
    initUserAnswerDataMap,
    resetQuestionStateOnNext,
  });

  return {
    isEnglish,
    subjectId,
    exerciseDisplayConfig,
    ...previewStore,

    // ===== 🔧 弹窗状态 =====
    confirmDialogState,

    // ===== 📊 题目状态 =====
    isAiCourseResume,
    rootQuestion,
    currentQuestion,
    questionStatus,
    rootQuestionRenderType,
    questionRenderType,
    rootQuestionStrategy,
    currentStrategy,
    hasNextQuestion,
    isSubmitting,
    streakCount,
    lastSubmitResult,
    isAnswerCompletedStatus,
    isShowExitConfirm,
    isExiting,
    // ===== 🎯 题目类型判断 =====
    isSelfEvaluationQuestion,
    isSystemGradedQuestion,
    isObjectiveQuestion,
    isSubjectiveQuestion,
    isChoiceQuestion,
    isMultipleChoiceQuestion,
    canSubmitSelfEvaluation,
    isParentChildQuestion,
    hasAnySelfEvalInWholeQuestion,
    // ===== 🔧 题目空数量 =====
    currentQuestionBlankCount,

    // ===== 🔧 判题结果 =====
    answerVerifMap,
    updateAnswerVerifMap,

    // ===== ⚙️ 配置参数 =====
    exerciseParams,

    // ===== 📈 进度和计时 =====
    progressBarState,
    updateProgressBarState,

    // ===== ⏱️ 计时器控制 =====
    timerState,
    resetCurrentQuestionTime,

    // ===== 🎬 转场动画控制 =====
    transitionState,
    updateTransitionState,

    updateUserAnswer,
    updateUserAnswerType,
    clearUserAnswer,

    // ===== 🔄 统一答案管理 =====
    userAnswerDataMap,
    userAnswerTypeMap,
    allQuestionMetaMap,
    updateSelfEvaluation,
    currentQuestionAnswers,

    // ===== 🧭 导航相关 =====
    currentInputMode,
    activeBlankIndex,
    activeQuestionIndex,

    // ===== 🔧 状态管理函数 =====
    updateQuestionStatus,
    resetQuestionStateOnNext,
    handleNextQuestion,
    navigationNextEvaluatingQuestionBlank,

    // ===== 📚 初始化数据 =====
    initialQuestionData,
    initialProgress,
    initialQuestionInfo,
    isLoading,
  };
};
